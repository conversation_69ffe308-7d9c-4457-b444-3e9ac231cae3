import { useRef } from 'react'
import type { ClothingItem } from '../types'
import './ImageUpload.css'

interface ImageUploadProps {
  label: string
  type: string
  image: ClothingItem | null
  onUpload: (type: any, file: File) => void
  onRemove: (type: any) => void
  disabled?: boolean
}

const ImageUpload = ({ label, type, image, onUpload, onRemove, disabled }: ImageUploadProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file.')
        return
      }
      
      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        alert('File size must be less than 10MB.')
        return
      }

      onUpload(type, file)
    }
  }

  const handleClick = () => {
    if (!disabled) {
      fileInputRef.current?.click()
    }
  }

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation()
    onRemove(type)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <div className={`image-upload ${disabled ? 'disabled' : ''}`}>
      <div className="upload-label">{label}</div>
      <div 
        className={`upload-area ${image ? 'has-image' : ''}`}
        onClick={handleClick}
      >
        {image ? (
          <div className="image-preview">
            <img src={image.preview} alt={`${label} preview`} />
            <div className="image-overlay">
              <button 
                className="remove-btn"
                onClick={handleRemove}
                disabled={disabled}
                type="button"
              >
                ×
              </button>
            </div>
            <div className="image-name">{image.name}</div>
          </div>
        ) : (
          <div className="upload-placeholder">
            <div className="upload-icon">📷</div>
            <div className="upload-text">
              <div>Click to upload</div>
              <div className="upload-subtext">PNG, JPG up to 10MB</div>
            </div>
          </div>
        )}
      </div>
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        style={{ display: 'none' }}
        disabled={disabled}
      />
    </div>
  )
}

export default ImageUpload
