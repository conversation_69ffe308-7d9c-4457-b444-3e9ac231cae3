import './ProgressBar.css'

interface ProgressBarProps {
  progress: number
}

const ProgressBar = ({ progress }: ProgressBarProps) => {
  return (
    <div className="progress-container">
      <div className="progress-label">
        Processing your clothing simulation... {progress}%
      </div>
      <div className="progress-bar">
        <div 
          className="progress-fill"
          style={{ width: `${progress}%` }}
        />
      </div>
      <div className="progress-steps">
        <div className={`step ${progress >= 25 ? 'completed' : ''}`}>
          Uploading images
        </div>
        <div className={`step ${progress >= 50 ? 'completed' : ''}`}>
          Processing clothing
        </div>
        <div className={`step ${progress >= 75 ? 'completed' : ''}`}>
          Generating simulation
        </div>
        <div className={`step ${progress >= 100 ? 'completed' : ''}`}>
          Complete
        </div>
      </div>
    </div>
  )
}

export default ProgressBar
