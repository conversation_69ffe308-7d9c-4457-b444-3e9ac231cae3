.progress-container {
  margin: 2rem 0;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 15px;
  border: 1px solid #e9ecef;
}

.progress-label {
  text-align: center;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.progress-bar {
  width: 100%;
  height: 12px;
  background: #e9ecef;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 1.5rem;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 6px;
  transition: width 0.3s ease;
  position: relative;
  overflow: hidden;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.progress-steps {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 0.5rem;
  text-align: center;
}

.step {
  padding: 0.5rem;
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
  position: relative;
  transition: all 0.3s ease;
}

.step.completed {
  color: #667eea;
  font-weight: 600;
}

.step.completed::before {
  content: '✓';
  position: absolute;
  top: -5px;
  left: 50%;
  transform: translateX(-50%);
  background: #667eea;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

@media (max-width: 768px) {
  .progress-container {
    margin: 1rem 0;
    padding: 1rem;
  }
  
  .progress-steps {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }
  
  .step {
    font-size: 0.8rem;
    padding: 0.75rem 0.25rem;
  }
  
  .progress-label {
    font-size: 1rem;
  }
}
