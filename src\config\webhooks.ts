// Webhook Configuration
// Replace these URLs with your actual Make.com webhook URLs

export const WEBHOOK_CONFIG = {
  // Replace with your Make.com webhook URL for sending images
  SEND_WEBHOOK_URL: 'https://hook.eu2.make.com/your-send-webhook-id',
  
  // Replace with your Make.com webhook URL for receiving results
  RECEIVE_WEBHOOK_URL: 'https://hook.eu2.make.com/your-receive-webhook-id',
  
  // Polling interval for checking results (in milliseconds)
  POLLING_INTERVAL: 2000,
  
  // Maximum polling attempts (30 attempts * 2 seconds = 60 seconds timeout)
  MAX_POLLING_ATTEMPTS: 30
}

// Instructions for setting up Make.com webhooks:
/*
1. SEND WEBHOOK SETUP:
   - Create a new scenario in Make.com
   - Add a "Webhooks" > "Custom webhook" module as the first step
   - Copy the webhook URL and replace SEND_WEBHOOK_URL above
   - Configure the webhook to receive JSON data with the following structure:
     {
       "sessionId": "string",
       "timestamp": "string",
       "images": {
         "headCloth": { "data": "base64", "filename": "string", "contentType": "string" },
         "topCloth": { "data": "base64", "filename": "string", "contentType": "string" },
         "legCloth": { "data": "base64", "filename": "string", "contentType": "string" },
         "shoes": { "data": "base64", "filename": "string", "contentType": "string" }
       }
     }

2. RECEIVE WEBHOOK SETUP:
   - Create another webhook module or use HTTP module to store results
   - The webhook should accept GET requests with sessionId parameter
   - Return JSON response with structure:
     {
       "success": true,
       "imageUrl": "https://your-result-image-url.com/image.jpg",
       "message": "Processing complete"
     }

3. PROCESSING FLOW:
   - Receive images from SEND_WEBHOOK_URL
   - Process the clothing simulation (integrate with your AI/image processing service)
   - Store the result with the sessionId
   - Make the result available via RECEIVE_WEBHOOK_URL

4. ALTERNATIVE DIRECT RESPONSE:
   - If your Make.com scenario can process and return results immediately,
   - You can modify the webhook service to use sendToWebhookDirect instead
   - This eliminates the need for polling and the RECEIVE_WEBHOOK_URL
*/
