.image-upload {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-label {
  font-weight: 600;
  font-size: 1.1rem;
  color: #333;
  margin-bottom: 0.5rem;
  text-align: center;
}

.upload-area {
  width: 100%;
  height: 200px;
  border: 2px dashed #ddd;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
  position: relative;
  overflow: hidden;
}

.upload-area:hover:not(.disabled .upload-area) {
  border-color: #667eea;
  background: #f8f9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
}

.upload-area.has-image {
  border: 2px solid #667eea;
  background: white;
  padding: 0;
}

.image-preview {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}

.image-preview img {
  width: 100%;
  height: calc(100% - 30px);
  object-fit: cover;
  border-radius: 13px 13px 0 0;
}

.image-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 2;
}

.remove-btn {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  color: #666;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.remove-btn:hover:not(:disabled) {
  background: #ff4757;
  color: white;
  transform: scale(1.1);
}

.remove-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.image-name {
  background: #667eea;
  color: white;
  padding: 0.5rem;
  font-size: 0.8rem;
  text-align: center;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0 0 13px 13px;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #666;
  padding: 1rem;
}

.upload-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  opacity: 0.7;
}

.upload-text {
  font-weight: 500;
  font-size: 1rem;
}

.upload-subtext {
  font-size: 0.8rem;
  color: #999;
  margin-top: 0.25rem;
}

.image-upload.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.image-upload.disabled .upload-area {
  cursor: not-allowed;
  background: #f5f5f5;
}

@media (max-width: 768px) {
  .upload-area {
    height: 150px;
  }
  
  .upload-icon {
    font-size: 1.5rem;
  }
  
  .upload-text {
    font-size: 0.9rem;
  }
  
  .upload-subtext {
    font-size: 0.7rem;
  }
}
