.clothing-simulator {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.upload-section h2 {
  text-align: center;
  margin-bottom: 2rem;
  font-size: 1.8rem;
  color: #333;
  font-weight: 600;
}

.person-upload-section {
  margin-bottom: 3rem;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea20, #764ba220);
  border-radius: 15px;
  border: 2px solid #667eea40;
}

.person-upload-section h3 {
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.4rem;
  color: #667eea;
  font-weight: 600;
}

.person-upload-container {
  display: flex;
  justify-content: center;
  max-width: 300px;
  margin: 0 auto;
}

.clothing-upload-section {
  margin-top: 2rem;
}

.clothing-upload-section h3 {
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.4rem;
  color: #333;
  font-weight: 600;
}

.upload-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.action-section {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid #eee;
}

.buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.fit-me-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  min-width: 150px;
}

.fit-me-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.fit-me-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.clear-btn {
  background: #f8f9fa;
  color: #666;
  border: 2px solid #dee2e6;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
}

.clear-btn:hover:not(:disabled) {
  background: #e9ecef;
  border-color: #adb5bd;
  transform: translateY(-1px);
}

.clear-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.error-message {
  background: #fee;
  color: #c33;
  padding: 1rem;
  border-radius: 10px;
  margin: 1rem 0;
  border: 1px solid #fcc;
  font-weight: 500;
}

@media (max-width: 768px) {
  .clothing-simulator {
    padding: 1rem;
    margin: 0 1rem 2rem;
  }

  .person-upload-section {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .person-upload-container {
    max-width: 100%;
  }

  .upload-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .buttons {
    flex-direction: column;
    align-items: center;
  }

  .fit-me-btn,
  .clear-btn {
    width: 100%;
    max-width: 300px;
  }
}
