import { useState } from 'react'
import ImageUpload from './ImageUpload'
import ProgressB<PERSON> from './ProgressBar'
import ResultDisplay from './ResultDisplay'
import type { ClothingItem, SimulatorState } from '../types'
import { sendToWebhook } from '../services/webhookService'
import './ClothingSimulator.css'

const ClothingSimulator = () => {
  const [state, setState] = useState<SimulatorState>({
    headCloth: null,
    topCloth: null,
    legCloth: null,
    shoes: null,
    isProcessing: false,
    progress: 0,
    resultImageUrl: null,
    error: null
  })

  const handleImageUpload = (type: keyof Pick<SimulatorState, 'headCloth' | 'topCloth' | 'legCloth' | 'shoes'>, file: File) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      const imageData = e.target?.result as string
      setState(prev => ({
        ...prev,
        [type]: {
          file,
          preview: imageData,
          name: file.name
        }
      }))
    }
    reader.readAsDataURL(file)
  }

  const handleRemoveImage = (type: keyof Pick<SimulatorState, 'headCloth' | 'topCloth' | 'legCloth' | 'shoes'>) => {
    setState(prev => ({
      ...prev,
      [type]: null
    }))
  }

  const handleFitMe = async () => {
    const { headCloth, topCloth, legCloth, shoes } = state
    
    // Check if at least one image is uploaded
    if (!headCloth && !topCloth && !legCloth && !shoes) {
      setState(prev => ({ ...prev, error: 'Please upload at least one clothing item.' }))
      return
    }

    setState(prev => ({ 
      ...prev, 
      isProcessing: true, 
      progress: 0, 
      error: null,
      resultImageUrl: null 
    }))

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setState(prev => ({
          ...prev,
          progress: Math.min(prev.progress + 10, 90)
        }))
      }, 500)

      // Send images to webhook
      const result = await sendToWebhook({
        headCloth: headCloth?.file || null,
        topCloth: topCloth?.file || null,
        legCloth: legCloth?.file || null,
        shoes: shoes?.file || null
      })

      clearInterval(progressInterval)
      
      setState(prev => ({
        ...prev,
        progress: 100,
        resultImageUrl: result.imageUrl,
        isProcessing: false
      }))

    } catch (error) {
      setState(prev => ({
        ...prev,
        isProcessing: false,
        progress: 0,
        error: error instanceof Error ? error.message : 'An error occurred while processing your request.'
      }))
    }
  }

  const handleClear = () => {
    setState({
      headCloth: null,
      topCloth: null,
      legCloth: null,
      shoes: null,
      isProcessing: false,
      progress: 0,
      resultImageUrl: null,
      error: null
    })
  }

  const canProcess = !state.isProcessing && (state.headCloth || state.topCloth || state.legCloth || state.shoes)

  return (
    <div className="clothing-simulator">
      <div className="upload-section">
        <h2>Upload Your Clothing Items</h2>
        <div className="upload-grid">
          <ImageUpload
            label="Head Cloth"
            type="headCloth"
            image={state.headCloth}
            onUpload={handleImageUpload}
            onRemove={handleRemoveImage}
            disabled={state.isProcessing}
          />
          <ImageUpload
            label="Top Cloth"
            type="topCloth"
            image={state.topCloth}
            onUpload={handleImageUpload}
            onRemove={handleRemoveImage}
            disabled={state.isProcessing}
          />
          <ImageUpload
            label="Leg Cloth"
            type="legCloth"
            image={state.legCloth}
            onUpload={handleImageUpload}
            onRemove={handleRemoveImage}
            disabled={state.isProcessing}
          />
          <ImageUpload
            label="Shoes"
            type="shoes"
            image={state.shoes}
            onUpload={handleImageUpload}
            onRemove={handleRemoveImage}
            disabled={state.isProcessing}
          />
        </div>
      </div>

      <div className="action-section">
        <div className="buttons">
          <button 
            className="fit-me-btn"
            onClick={handleFitMe}
            disabled={!canProcess}
          >
            {state.isProcessing ? 'Processing...' : 'Fit Me!'}
          </button>
          <button 
            className="clear-btn"
            onClick={handleClear}
            disabled={state.isProcessing}
          >
            Clear All
          </button>
        </div>

        {state.error && (
          <div className="error-message">
            {state.error}
          </div>
        )}

        {state.isProcessing && (
          <ProgressBar progress={state.progress} />
        )}
      </div>

      {state.resultImageUrl && (
        <ResultDisplay imageUrl={state.resultImageUrl} />
      )}
    </div>
  )
}

export default ClothingSimulator
