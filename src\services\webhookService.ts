import type { WebhookPayload, WebhookResponse } from '../types'
import { WEBHOOK_CONFIG } from '../config/webhooks'

// Convert file to base64
const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => {
      const result = reader.result as string
      // Remove the data:image/jpeg;base64, prefix
      const base64 = result.split(',')[1]
      resolve(base64)
    }
    reader.onerror = error => reject(error)
  })
}

// Generate a unique session ID for tracking the request
const generateSessionId = (): string => {
  return `fitme_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
}

// Send images to Make.com webhook (direct response version)
export const sendToWebhook = async (payload: WebhookPayload): Promise<WebhookResponse> => {
  try {
    const sessionId = generateSessionId()

    // Prepare the payload with base64 encoded images
    const webhookPayload: any = {
      sessionId,
      timestamp: new Date().toISOString(),
      images: {}
    }

    // Convert files to base64 and add to payload
    if (payload.personImage) {
      webhookPayload.images.personImage = {
        data: await fileToBase64(payload.personImage),
        filename: payload.personImage.name,
        contentType: payload.personImage.type
      }
    }

    if (payload.headCloth) {
      webhookPayload.images.headCloth = {
        data: await fileToBase64(payload.headCloth),
        filename: payload.headCloth.name,
        contentType: payload.headCloth.type
      }
    }

    if (payload.topCloth) {
      webhookPayload.images.topCloth = {
        data: await fileToBase64(payload.topCloth),
        filename: payload.topCloth.name,
        contentType: payload.topCloth.type
      }
    }

    if (payload.legCloth) {
      webhookPayload.images.legCloth = {
        data: await fileToBase64(payload.legCloth),
        filename: payload.legCloth.name,
        contentType: payload.legCloth.type
      }
    }

    if (payload.shoes) {
      webhookPayload.images.shoes = {
        data: await fileToBase64(payload.shoes),
        filename: payload.shoes.name,
        contentType: payload.shoes.type
      }
    }

    // Send to Make.com webhook
    const response = await fetch(WEBHOOK_CONFIG.SEND_WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(webhookPayload)
    })

    if (!response.ok) {
      throw new Error(`Webhook request failed: ${response.status} ${response.statusText}`)
    }

    // Try to get response data
    let responseData
    try {
      responseData = await response.json()
    } catch {
      // If no JSON response, assume success and return a placeholder
      responseData = { success: true }
    }

    // If Make.com returns the result immediately
    if (responseData.imageUrl) {
      return {
        imageUrl: responseData.imageUrl,
        success: true,
        message: responseData.message || 'Processing complete'
      }
    }

    // For now, return a success message since we don't have the receive webhook yet
    return {
      imageUrl: 'https://via.placeholder.com/400x600/667eea/ffffff?text=Simulation+Complete%0AResult+will+be+available+soon',
      success: true,
      message: 'Images sent successfully to Make.com! The result will be processed and available soon.'
    }

  } catch (error) {
    console.error('Webhook service error:', error)
    throw new Error(error instanceof Error ? error.message : 'Failed to process clothing simulation')
  }
}

// Note: Polling function removed to avoid CORS issues
// When you set up the receive webhook, you can implement polling or use a different approach

// Alternative: Direct webhook response (if Make.com returns result immediately)
export const sendToWebhookDirect = async (payload: WebhookPayload): Promise<WebhookResponse> => {
  try {
    // Prepare FormData for multipart/form-data upload
    const formData = new FormData()
    
    if (payload.headCloth) {
      formData.append('headCloth', payload.headCloth)
    }
    
    if (payload.topCloth) {
      formData.append('topCloth', payload.topCloth)
    }
    
    if (payload.legCloth) {
      formData.append('legCloth', payload.legCloth)
    }
    
    if (payload.shoes) {
      formData.append('shoes', payload.shoes)
    }

    const response = await fetch(WEBHOOK_CONFIG.SEND_WEBHOOK_URL, {
      method: 'POST',
      body: formData
    })

    if (!response.ok) {
      throw new Error(`Webhook request failed: ${response.status} ${response.statusText}`)
    }

    const result = await response.json()
    
    if (result.success && result.imageUrl) {
      return {
        imageUrl: result.imageUrl,
        success: true,
        message: result.message
      }
    }
    
    throw new Error(result.error || 'Unknown error occurred')

  } catch (error) {
    console.error('Direct webhook service error:', error)
    throw new Error(error instanceof Error ? error.message : 'Failed to process clothing simulation')
  }
}
