import type { WebhookPayload, WebhookResponse } from '../types'
import { WEBHOOK_CONFIG } from '../config/webhooks'

// Convert file to base64
const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => {
      const result = reader.result as string
      // Remove the data:image/jpeg;base64, prefix
      const base64 = result.split(',')[1]
      resolve(base64)
    }
    reader.onerror = error => reject(error)
  })
}

// Generate a unique session ID for tracking the request
const generateSessionId = (): string => {
  return `fitme_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
}

// Send images to Make.com webhook (direct response version)
export const sendToWebhook = async (payload: WebhookPayload): Promise<WebhookResponse> => {
  try {
    const sessionId = generateSessionId()

    // Try FormData approach first (often works better with Make.com)
    const formData = new FormData()
    formData.append('sessionId', sessionId)
    formData.append('timestamp', new Date().toISOString())

    // Add files directly to FormData
    if (payload.personImage) {
      formData.append('personImage', payload.personImage, payload.personImage.name)
    }
    if (payload.headCloth) {
      formData.append('headCloth', payload.headCloth, payload.headCloth.name)
    }
    if (payload.topCloth) {
      formData.append('topCloth', payload.topCloth, payload.topCloth.name)
    }
    if (payload.legCloth) {
      formData.append('legCloth', payload.legCloth, payload.legCloth.name)
    }
    if (payload.shoes) {
      formData.append('shoes', payload.shoes, payload.shoes.name)
    }

    console.log('Sending FormData to webhook:', WEBHOOK_CONFIG.SEND_WEBHOOK_URL)
    console.log('FormData entries:', Array.from(formData.entries()).map(([key, value]) => ({
      key,
      type: value instanceof File ? 'File' : 'string',
      name: value instanceof File ? value.name : value
    })))

    // Send FormData to Make.com webhook
    const response = await fetch(WEBHOOK_CONFIG.SEND_WEBHOOK_URL, {
      method: 'POST',
      body: formData // Don't set Content-Type header, let browser set it with boundary
    })

    console.log('Webhook response status:', response.status)
    console.log('Webhook response headers:', Object.fromEntries(response.headers.entries()))

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Webhook error response:', errorText)

      // If FormData fails, try JSON approach as fallback
      console.log('FormData failed, trying JSON approach...')
      return await sendToWebhookJSON(payload, sessionId)
    }

    // Try to get response data
    let responseData
    try {
      const responseText = await response.text()
      console.log('Webhook response text:', responseText)
      responseData = responseText ? JSON.parse(responseText) : { success: true }
    } catch (parseError) {
      console.log('Response is not JSON, treating as success')
      responseData = { success: true }
    }

    // If Make.com returns the result immediately
    if (responseData.imageUrl) {
      return {
        imageUrl: responseData.imageUrl,
        success: true,
        message: responseData.message || 'Processing complete'
      }
    }

    // For now, return a success message since we don't have the receive webhook yet
    // Create a simple SVG data URL without base64 encoding to avoid Unicode issues
    const placeholderSvg = `<svg width="400" height="600" xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="#667eea"/><text x="50%" y="40%" text-anchor="middle" fill="white" font-family="Arial,sans-serif" font-size="24" font-weight="bold">Success!</text><text x="50%" y="50%" text-anchor="middle" fill="white" font-family="Arial,sans-serif" font-size="16">Images sent to Make.com</text><text x="50%" y="60%" text-anchor="middle" fill="white" font-family="Arial,sans-serif" font-size="14">Processing in progress...</text><text x="50%" y="75%" text-anchor="middle" fill="white" font-family="Arial,sans-serif" font-size="12">Check your Make.com scenario</text><text x="50%" y="85%" text-anchor="middle" fill="white" font-family="Arial,sans-serif" font-size="12">for the processed result</text></svg>`

    // Use URL encoding instead of base64 to avoid Unicode issues
    const placeholderDataUrl = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(placeholderSvg)}`

    return {
      imageUrl: placeholderDataUrl,
      success: true,
      message: 'Images sent successfully to Make.com! Check your Make.com scenario for the processing result.'
    }

  } catch (error) {
    console.error('Webhook service error:', error)
    throw new Error(error instanceof Error ? error.message : 'Failed to process clothing simulation')
  }
}

// Fallback JSON approach
const sendToWebhookJSON = async (payload: WebhookPayload, sessionId: string): Promise<WebhookResponse> => {
  // Prepare the payload with base64 encoded images
  const webhookPayload: any = {
    sessionId,
    timestamp: new Date().toISOString(),
    images: {}
  }

  // Convert files to base64 and add to payload
  if (payload.personImage) {
    webhookPayload.images.personImage = {
      data: await fileToBase64(payload.personImage),
      filename: payload.personImage.name,
      contentType: payload.personImage.type
    }
  }

  if (payload.headCloth) {
    webhookPayload.images.headCloth = {
      data: await fileToBase64(payload.headCloth),
      filename: payload.headCloth.name,
      contentType: payload.headCloth.type
    }
  }

  if (payload.topCloth) {
    webhookPayload.images.topCloth = {
      data: await fileToBase64(payload.topCloth),
      filename: payload.topCloth.name,
      contentType: payload.topCloth.type
    }
  }

  if (payload.legCloth) {
    webhookPayload.images.legCloth = {
      data: await fileToBase64(payload.legCloth),
      filename: payload.legCloth.name,
      contentType: payload.legCloth.type
    }
  }

  if (payload.shoes) {
    webhookPayload.images.shoes = {
      data: await fileToBase64(payload.shoes),
      filename: payload.shoes.name,
      contentType: payload.shoes.type
    }
  }

  // Log the payload for debugging
  console.log('Sending JSON to webhook:', WEBHOOK_CONFIG.SEND_WEBHOOK_URL)
  console.log('Payload structure:', {
    sessionId: webhookPayload.sessionId,
    timestamp: webhookPayload.timestamp,
    imageCount: Object.keys(webhookPayload.images).length,
    imageTypes: Object.keys(webhookPayload.images)
  })

  // Send to Make.com webhook
  const response = await fetch(WEBHOOK_CONFIG.SEND_WEBHOOK_URL, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(webhookPayload)
  })

  console.log('JSON Webhook response status:', response.status)

  if (!response.ok) {
    const errorText = await response.text()
    console.error('JSON Webhook error response:', errorText)
    throw new Error(`JSON Webhook request failed: ${response.status} ${response.statusText} - ${errorText}`)
  }

  // Try to get response data
  let responseData
  try {
    const responseText = await response.text()
    console.log('JSON Webhook response text:', responseText)
    responseData = responseText ? JSON.parse(responseText) : { success: true }
  } catch (parseError) {
    console.log('JSON Response is not JSON, treating as success')
    responseData = { success: true }
  }

  // If Make.com returns the result immediately
  if (responseData.imageUrl) {
    return {
      imageUrl: responseData.imageUrl,
      success: true,
      message: responseData.message || 'Processing complete'
    }
  }

  // Return success placeholder
  const placeholderSvg = `<svg width="400" height="600" xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="#667eea"/><text x="50%" y="40%" text-anchor="middle" fill="white" font-family="Arial,sans-serif" font-size="24" font-weight="bold">Success!</text><text x="50%" y="50%" text-anchor="middle" fill="white" font-family="Arial,sans-serif" font-size="16">Images sent to Make.com</text><text x="50%" y="60%" text-anchor="middle" fill="white" font-family="Arial,sans-serif" font-size="14">Processing in progress...</text><text x="50%" y="75%" text-anchor="middle" fill="white" font-family="Arial,sans-serif" font-size="12">Check your Make.com scenario</text><text x="50%" y="85%" text-anchor="middle" fill="white" font-family="Arial,sans-serif" font-size="12">for the processed result</text></svg>`
  const placeholderDataUrl = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(placeholderSvg)}`

  return {
    imageUrl: placeholderDataUrl,
    success: true,
    message: 'Images sent successfully to Make.com via JSON! Check your Make.com scenario for the processing result.'
  }
}

// Note: Polling function removed to avoid CORS issues
// When you set up the receive webhook, you can implement polling or use a different approach

// Alternative: Direct webhook response (if Make.com returns result immediately)
export const sendToWebhookDirect = async (payload: WebhookPayload): Promise<WebhookResponse> => {
  try {
    // Prepare FormData for multipart/form-data upload
    const formData = new FormData()
    
    if (payload.headCloth) {
      formData.append('headCloth', payload.headCloth)
    }
    
    if (payload.topCloth) {
      formData.append('topCloth', payload.topCloth)
    }
    
    if (payload.legCloth) {
      formData.append('legCloth', payload.legCloth)
    }
    
    if (payload.shoes) {
      formData.append('shoes', payload.shoes)
    }

    const response = await fetch(WEBHOOK_CONFIG.SEND_WEBHOOK_URL, {
      method: 'POST',
      body: formData
    })

    if (!response.ok) {
      throw new Error(`Webhook request failed: ${response.status} ${response.statusText}`)
    }

    const result = await response.json()
    
    if (result.success && result.imageUrl) {
      return {
        imageUrl: result.imageUrl,
        success: true,
        message: result.message
      }
    }
    
    throw new Error(result.error || 'Unknown error occurred')

  } catch (error) {
    console.error('Direct webhook service error:', error)
    throw new Error(error instanceof Error ? error.message : 'Failed to process clothing simulation')
  }
}
