import { WebhookPayload, WebhookResponse } from '../types/clothing'
import { WEBHOOK_CONFIG } from '../config/webhooks'

// Convert file to base64
const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => {
      const result = reader.result as string
      // Remove the data:image/jpeg;base64, prefix
      const base64 = result.split(',')[1]
      resolve(base64)
    }
    reader.onerror = error => reject(error)
  })
}

// Generate a unique session ID for tracking the request
const generateSessionId = (): string => {
  return `fitme_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// Send images to Make.com webhook
export const sendToWebhook = async (payload: WebhookPayload): Promise<WebhookResponse> => {
  try {
    const sessionId = generateSessionId()
    
    // Prepare the payload with base64 encoded images
    const webhookPayload: any = {
      sessionId,
      timestamp: new Date().toISOString(),
      images: {}
    }

    // Convert files to base64 and add to payload
    if (payload.headCloth) {
      webhookPayload.images.headCloth = {
        data: await fileToBase64(payload.headCloth),
        filename: payload.headCloth.name,
        contentType: payload.headCloth.type
      }
    }

    if (payload.topCloth) {
      webhookPayload.images.topCloth = {
        data: await fileToBase64(payload.topCloth),
        filename: payload.topCloth.name,
        contentType: payload.topCloth.type
      }
    }

    if (payload.legCloth) {
      webhookPayload.images.legCloth = {
        data: await fileToBase64(payload.legCloth),
        filename: payload.legCloth.name,
        contentType: payload.legCloth.type
      }
    }

    if (payload.shoes) {
      webhookPayload.images.shoes = {
        data: await fileToBase64(payload.shoes),
        filename: payload.shoes.name,
        contentType: payload.shoes.type
      }
    }

    // Send to Make.com webhook
    const response = await fetch(WEBHOOK_CONFIG.SEND_WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(webhookPayload)
    })

    if (!response.ok) {
      throw new Error(`Webhook request failed: ${response.status} ${response.statusText}`)
    }

    // Poll for results
    const result = await pollForResult(sessionId)
    return result

  } catch (error) {
    console.error('Webhook service error:', error)
    throw new Error(error instanceof Error ? error.message : 'Failed to process clothing simulation')
  }
}

// Poll for results from Make.com
const pollForResult = async (sessionId: string): Promise<WebhookResponse> => {
  let attempts = 0
  
  while (attempts < WEBHOOK_CONFIG.MAX_POLLING_ATTEMPTS) {
    try {
      // Wait before polling
      await new Promise(resolve => setTimeout(resolve, WEBHOOK_CONFIG.POLLING_INTERVAL))
      
      // Check for results
      const response = await fetch(`${WEBHOOK_CONFIG.RECEIVE_WEBHOOK_URL}?sessionId=${sessionId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      if (response.ok) {
        const result = await response.json()
        
        if (result.success && result.imageUrl) {
          return {
            imageUrl: result.imageUrl,
            success: true,
            message: result.message
          }
        }
        
        if (result.error) {
          throw new Error(result.error)
        }
      }
      
      attempts++
    } catch (error) {
      console.error(`Polling attempt ${attempts + 1} failed:`, error)
      attempts++
    }
  }
  
  throw new Error('Processing timeout. Please try again.')
}

// Alternative: Direct webhook response (if Make.com returns result immediately)
export const sendToWebhookDirect = async (payload: WebhookPayload): Promise<WebhookResponse> => {
  try {
    // Prepare FormData for multipart/form-data upload
    const formData = new FormData()
    
    if (payload.headCloth) {
      formData.append('headCloth', payload.headCloth)
    }
    
    if (payload.topCloth) {
      formData.append('topCloth', payload.topCloth)
    }
    
    if (payload.legCloth) {
      formData.append('legCloth', payload.legCloth)
    }
    
    if (payload.shoes) {
      formData.append('shoes', payload.shoes)
    }

    const response = await fetch(WEBHOOK_CONFIG.SEND_WEBHOOK_URL, {
      method: 'POST',
      body: formData
    })

    if (!response.ok) {
      throw new Error(`Webhook request failed: ${response.status} ${response.statusText}`)
    }

    const result = await response.json()
    
    if (result.success && result.imageUrl) {
      return {
        imageUrl: result.imageUrl,
        success: true,
        message: result.message
      }
    }
    
    throw new Error(result.error || 'Unknown error occurred')

  } catch (error) {
    console.error('Direct webhook service error:', error)
    throw new Error(error instanceof Error ? error.message : 'Failed to process clothing simulation')
  }
}
