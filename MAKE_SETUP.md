# Make.com Webhook Setup Guide

## Struttura Dati Inviati

Il tuo webhook riceverà i seguenti dati in formato JSON:

```json
{
  "sessionId": "fitme_1757070159277_abc123",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "images": {
    "personImage": {
      "data": "base64-encoded-image-data",
      "filename": "person.jpg",
      "contentType": "image/jpeg"
    },
    "headCloth": {
      "data": "base64-encoded-image-data", 
      "filename": "hat.jpg",
      "contentType": "image/jpeg"
    },
    "topCloth": {
      "data": "base64-encoded-image-data",
      "filename": "shirt.jpg", 
      "contentType": "image/jpeg"
    },
    "legCloth": {
      "data": "base64-encoded-image-data",
      "filename": "pants.jpg",
      "contentType": "image/jpeg"
    },
    "shoes": {
      "data": "base64-encoded-image-data",
      "filename": "shoes.jpg",
      "contentType": "image/jpeg"
    }
  }
}
```

## Setup Make.com Scenario

### 1. Webhook Module
- Aggiungi un modulo "Webhooks" > "Custom webhook"
- Il tuo URL è già configurato: `https://hook.us2.make.com/296ixyrtovct2mlrv413oo6rybmh8fh7`

### 2. Processare le Immagini
- Usa il modulo "Base64" per decodificare le immagini
- Ogni immagine ha: `data` (base64), `filename`, `contentType`

### 3. Esempio di Processing
```
1. Webhook riceve i dati
2. Decodifica le immagini base64
3. Salva le immagini temporaneamente
4. Invia le immagini al tuo servizio AI/processing
5. Ricevi l'immagine risultato
6. (Opzionale) Invia il risultato tramite un altro webhook
```

## Test del Webhook

Per testare se il webhook funziona:

1. Vai su Make.com
2. Apri il tuo scenario
3. Controlla i log di esecuzione
4. Verifica che i dati arrivino correttamente

## Debugging

Se il webhook non riceve dati:
- Controlla che il scenario sia attivo
- Verifica l'URL del webhook
- Controlla i log di Make.com per errori
- Assicurati che il webhook accetti richieste POST con JSON

## Prossimi Passi

1. ✅ Webhook di invio configurato
2. ⏳ Implementa il processing delle immagini in Make.com
3. ⏳ (Opzionale) Configura webhook di risposta per risultati automatici

## Note CORS

Il problema CORS è stato risolto rimuovendo il polling. L'app ora:
- Invia le immagini al tuo webhook
- Mostra un messaggio di successo
- Non tenta di recuperare automaticamente il risultato (evita CORS)

Se vuoi implementare il recupero automatico del risultato, dovrai:
1. Configurare un secondo webhook per le risposte
2. Implementare CORS headers nel tuo scenario Make.com
3. O usare un approccio diverso (es. WebSocket, Server-Sent Events)
