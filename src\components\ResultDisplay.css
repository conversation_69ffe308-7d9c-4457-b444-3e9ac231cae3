.result-display {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  margin-top: 2rem;
  text-align: center;
}

.result-display h2 {
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
  color: #333;
  font-weight: 600;
}

.result-container {
  position: relative;
  display: inline-block;
  max-width: 100%;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  background: #f8f9fa;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.result-container img {
  max-width: 100%;
  max-height: 600px;
  width: auto;
  height: auto;
  border-radius: 15px;
  object-fit: contain;
}

.loading-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e9ecef;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #666;
}

.error-placeholder div:first-child {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.error-placeholder button {
  margin-top: 1rem;
  background: #667eea;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.error-placeholder button:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}

.result-actions {
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.download-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.download-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

@media (max-width: 768px) {
  .result-display {
    padding: 1rem;
    margin: 1rem;
  }
  
  .result-container {
    min-height: 200px;
  }
  
  .loading-placeholder,
  .error-placeholder {
    padding: 2rem 1rem;
  }
  
  .result-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .download-btn {
    width: 100%;
    max-width: 300px;
  }
}
