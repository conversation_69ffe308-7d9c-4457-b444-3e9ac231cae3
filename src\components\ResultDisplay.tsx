import { useState } from 'react'
import './ResultDisplay.css'

interface ResultDisplayProps {
  imageUrl: string
}

const ResultDisplay = ({ imageUrl }: ResultDisplayProps) => {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  const handleImageLoad = () => {
    setIsLoading(false)
    setHasError(false)
  }

  const handleImageError = () => {
    setIsLoading(false)
    setHasError(true)
  }

  const handleDownload = () => {
    const link = document.createElement('a')
    link.href = imageUrl
    link.download = 'fitme-simulation.jpg'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <div className="result-display">
      <h2>Your Clothing Simulation</h2>
      <div className="result-container">
        {isLoading && (
          <div className="loading-placeholder">
            <div className="loading-spinner"></div>
            <div>Loading your simulation...</div>
          </div>
        )}
        
        {hasError ? (
          <div className="error-placeholder">
            <div>❌</div>
            <div>Failed to load the simulation image</div>
            <button onClick={() => window.location.reload()}>
              Try Again
            </button>
          </div>
        ) : (
          <img
            src={imageUrl}
            alt="Clothing simulation result"
            onLoad={handleImageLoad}
            onError={handleImageError}
            style={{ display: isLoading ? 'none' : 'block' }}
          />
        )}
      </div>
      
      {!isLoading && !hasError && (
        <div className="result-actions">
          <button className="download-btn" onClick={handleDownload}>
            Download Image
          </button>
        </div>
      )}
    </div>
  )
}

export default ResultDisplay
