export interface ClothingItem {
  file: File
  preview: string
  name: string
}

export interface SimulatorState {
  headCloth: ClothingItem | null
  topCloth: ClothingItem | null
  legCloth: ClothingItem | null
  shoes: ClothingItem | null
  isProcessing: boolean
  progress: number
  resultImageUrl: string | null
  error: string | null
}

export interface WebhookPayload {
  headCloth: File | null
  topCloth: File | null
  legCloth: File | null
  shoes: File | null
}

export interface WebhookResponse {
  imageUrl: string
  success: boolean
  message?: string
}
